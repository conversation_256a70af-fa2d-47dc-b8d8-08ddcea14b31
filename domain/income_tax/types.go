package incometax

import (
	"time"

	"github.com/Sera-Global/be-nbs-accounting-system/common/types"
)

type IncomeTax struct {
	ID        int64      `gorm:"column:id;primary_key"`
	Code      string     `gorm:"column:code;unique"`
	MoreThan  float64    `gorm:"column:more_than"`
	LessThan  float64    `gorm:"column:less_than"`
	Amount    string     `gorm:"column:amount"`
	CreatedAt time.Time  `gorm:"column:created_at"`
	UpdatedAt time.Time  `gorm:"column:updated_at"`
	DeletedAt *time.Time `gorm:"column:deleted_at"`
}

type GetListReq struct {
	types.BasicGetParam
}

// TaxAmountItem represents a single tax amount entry in the JSON array
type TaxAmountItem struct {
	Type  string  `json:"type"`
	Price float64 `json:"price"`
}

// GetByAmountParam represents the parameters for getting income tax by amount
type GetByAmountParam struct {
	Amount float64
}
