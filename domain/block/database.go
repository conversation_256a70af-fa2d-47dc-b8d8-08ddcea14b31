package block

import (
	"context"
	"errors"

	"github.com/Sera-Global/be-nbs-accounting-system/common/log"
	"github.com/Sera-Global/be-nbs-accounting-system/pkg/lib/dbmanager"
	"github.com/lib/pq"
	"gorm.io/gorm"
)

// getListWithDistricts fetches all blocks with their associated districts ordered by block name.
func (rsc BlockResource) getListWithDistricts(ctx context.Context, param GetListReq) ([]BlockWithDistricts, error) {
	db := dbmanager.Manager().WithContext(ctx)

	// Build the query to get blocks with their districts
	query := `
		SELECT 
			block.id,
			block.name,
			COALESCE(
				ARRAY_AGG(
					district.name ORDER BY district.name ASC
				) FILTER (WHERE district.name IS NOT NULL), 
				'{}'
			) as districts
		FROM block
		LEFT JOIN district ON block.id = district.block_id AND district.deleted_at IS NULL
		WHERE block.deleted_at IS NULL`

	// Add search filter if provided
	args := []interface{}{}
	if param.Search != "" {
		query += " AND block.name ILIKE ?"
		args = append(args, "%"+param.Search+"%")
	}

	query += `
		GROUP BY block.id, block.name
		ORDER BY block.name ASC`

	// Execute the query
	rows, err := db.Raw(query, args...).Rows()
	if err != nil {
		return []BlockWithDistricts{}, log.LogError(err, nil)
	}
	defer rows.Close()

	var blocks []BlockWithDistricts
	for rows.Next() {
		var block BlockWithDistricts
		var districts pq.StringArray

		err := rows.Scan(&block.ID, &block.Name, &districts)
		if err != nil {
			return []BlockWithDistricts{}, log.LogError(err, nil)
		}

		// Convert pq.StringArray to []string
		distSlice := []string(districts)

		// Handle empty districts array
		if len(distSlice) == 1 && distSlice[0] == "" {
			distSlice = []string{}
		}

		block.Districts = distSlice
		blocks = append(blocks, block)
	}

	if err = rows.Err(); err != nil {
		return []BlockWithDistricts{}, log.LogError(err, nil)
	}

	return blocks, nil
}

// getByID fetches block by ID.
func (rsc BlockResource) getByID(ctx context.Context, param GetByIDParam) (Block, error) {
	var block Block

	db := dbmanager.Manager().WithContext(ctx)

	err := db.Where("id = ?", param.ID).
		First(&block).Error

	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return Block{}, nil
		}
		return Block{}, log.LogError(err, nil)
	}

	return block, nil
}
