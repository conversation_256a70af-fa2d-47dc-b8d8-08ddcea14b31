package block

import (
	"time"

	"github.com/Sera-Global/be-nbs-accounting-system/common/types"
)

type Block struct {
	ID             int64      `gorm:"column:id;primary_key"`
	Code           string     `gorm:"column:code;unique"`
	Name           string     `gorm:"column:name"`
	UnitPrice      float64    `gorm:"column:unit_price"`
	PricePerWorker float64    `gorm:"column:price_per_worker"`
	CreatedAt      time.Time  `gorm:"column:created_at"`
	UpdatedAt      time.Time  `gorm:"column:updated_at"`
	DeletedAt      *time.Time `gorm:"column:deleted_at"`
}

// GetListReq represents the request parameters for getting block list
type GetListReq struct {
	types.BasicGetParam
}

// GetByIDParam represents the parameters for getting block by ID
type GetByIDParam struct {
	ID int64
}

// BlockWithDistricts represents a block with its associated districts
type BlockWithDistricts struct {
	ID        int64    `json:"id"`
	Name      string   `json:"name"`
	Districts []string `json:"district"`
}
