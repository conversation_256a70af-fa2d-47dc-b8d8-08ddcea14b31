package departmentpic

import (
	"context"
	"errors"

	"github.com/Sera-Global/be-nbs-accounting-system/common/log"
	"github.com/Sera-Global/be-nbs-accounting-system/pkg/lib/dbmanager"
	"gorm.io/gorm"
)

// getListByDepartmentID fetches all department PICs for a specific department ordered by pic_name.
func (rsc DepartmentPicResource) getListByDepartmentID(ctx context.Context, param GetListByDepartmentIDParam) ([]DepartmentPic, error) {
	var departmentPics []DepartmentPic

	db := dbmanager.Manager().WithContext(ctx)

	if param.Search != "" {
		db = db.Where("pic_name ILIKE ?", "%"+param.Search+"%")
	}

	// Exclude soft deleted records, filter by department_id, and order by pic_name ascending
	err := db.Where("deleted_at IS NULL AND department_id = ?", param.DepartmentID).
		Order("pic_name ASC").
		Find(&departmentPics).Error

	if err != nil {
		return []DepartmentPic{}, log.LogError(err, nil)
	}

	return departmentPics, nil
}

// getByIDWithCustomer fetches department PIC by ID with customer information.
func (rsc DepartmentPicResource) getByIDWithCustomer(ctx context.Context, param GetByIDWithCustomerParam) (DepartmentPicWithCustomer, error) {
	var result DepartmentPicWithCustomer

	db := dbmanager.Manager().WithContext(ctx)

	// Join department_pic -> department -> customer to get customer_id
	err := db.Table("department_pic").
		Select("department_pic.id, department_pic.department_id, department_pic.pic_name, department.customer_id, department.name as department_name, customer.name as customer_name").
		Joins("INNER JOIN department ON department_pic.department_id = department.id").
		Joins("INNER JOIN customer ON department.customer_id = customer.id").
		Where("department_pic.id = ?", param.ID).
		First(&result).Error

	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return DepartmentPicWithCustomer{}, nil
		}
		return DepartmentPicWithCustomer{}, log.LogError(err, nil)
	}

	return result, nil
}
