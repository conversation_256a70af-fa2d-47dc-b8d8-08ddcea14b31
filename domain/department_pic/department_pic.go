package departmentpic

import (
	"context"

	"github.com/Sera-Global/be-nbs-accounting-system/common/log"
)

type (
	DepartmentPicDomainItf interface {
		GetListByDepartmentID(ctx context.Context, param GetListByDepartmentIDParam) ([]DepartmentPic, error)
		GetByIDWithCustomer(ctx context.Context, param GetByIDWithCustomerParam) (DepartmentPicWithCustomer, error)
	}

	DepartmentPicResourceItf interface {
		getListByDepartmentID(ctx context.Context, param GetListByDepartmentIDParam) ([]DepartmentPic, error)
		getByIDWithCustomer(ctx context.Context, param GetByIDWithCustomerParam) (DepartmentPicWithCustomer, error)
	}
)

// GetListByDepartmentID retrieves all department PICs for a specific department ordered by pic_name.
func (d *DepartmentPicDomain) GetListByDepartmentID(ctx context.Context, param GetListByDepartmentIDParam) ([]DepartmentPic, error) {
	departmentPics, err := d.resource.getListByDepartmentID(ctx, param)
	if err != nil {
		return []DepartmentPic{}, log.LogError(err, nil)
	}
	return departmentPics, nil
}

// GetByIDWithCustomer retrieves department PIC by ID with customer information.
func (d *DepartmentPicDomain) GetByIDWithCustomer(ctx context.Context, param GetByIDWithCustomerParam) (DepartmentPicWithCustomer, error) {
	departmentPic, err := d.resource.getByIDWithCustomer(ctx, param)
	if err != nil {
		return DepartmentPicWithCustomer{}, log.LogError(err, nil)
	}
	return departmentPic, nil
}
