package dailyreportaddition

import (
	"context"

	"github.com/Sera-Global/be-nbs-accounting-system/common/log"
)

type (
	DailyReportAdditionDomainItf interface {
		GetByID(ctx context.Context, param GetByIDParam) (DailyReportAddition, error)
		GetList(ctx context.Context, param GetListReq) ([]DailyReportAddition, error)
	}

	DailyReportAdditionResourceItf interface {
		getByID(ctx context.Context, param GetByIDParam) (DailyReportAddition, error)
		getList(ctx context.Context, param GetListReq) ([]DailyReportAddition, error)
	}
)

// GetByID retrieves daily report addition record by ID.
func (d *DailyReportAdditionDomain) GetByID(ctx context.Context, param GetByIDParam) (DailyReportAddition, error) {
	addition, err := d.resource.getByID(ctx, param)
	if err != nil {
		return DailyReportAddition{}, log.LogError(err, nil)
	}
	return addition, nil
}

// GetList retrieves daily report addition records with optional search filtering.
func (d *DailyReportAdditionDomain) GetList(ctx context.Context, param GetListReq) ([]DailyReportAddition, error) {
	additions, err := d.resource.getList(ctx, param)
	if err != nil {
		return []DailyReportAddition{}, log.LogError(err, nil)
	}
	return additions, nil
}
