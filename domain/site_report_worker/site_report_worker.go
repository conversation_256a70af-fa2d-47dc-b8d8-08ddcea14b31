package sitereportworker

import (
	"context"

	"github.com/Sera-Global/be-nbs-accounting-system/common/log"
	"gorm.io/gorm"
)

type (
	SiteReportWorkerDomainItf interface {
		CreateWorkerWithTx(ctx context.Context, tx *gorm.DB, param CreateWorkerParam) (int64, error)
		UpdateWorkerWithTx(ctx context.Context, tx *gorm.DB, param UpdateWorkerParam) error
		DeleteWorkerWithTx(ctx context.Context, tx *gorm.DB, param DeleteWorkerParam) error
		GetBySiteReportID(ctx context.Context, siteReportID int64) ([]SiteReportWorker, error)
		GetWorkerReportList(ctx context.Context, param GetWorkerReportListParam) ([]WorkerReportListItem, error)
	}

	SiteReportWorkerResourceItf interface {
		createWorkerWithTx(ctx context.Context, tx *gorm.DB, param CreateWorkerParam) (int64, error)
		updateWorkerWithTx(ctx context.Context, tx *gorm.DB, param UpdateWorkerParam) error
		deleteWorkerWithTx(ctx context.Context, tx *gorm.DB, param DeleteWorkerParam) error
		getBySiteReportID(ctx context.Context, siteReportID int64) ([]SiteReportWorker, error)
		getWorkerReportList(ctx context.Context, param GetWorkerReportListParam) ([]WorkerReportListItem, error)
	}
)

// CreateWorkerWithTx creates a new site report worker record within a transaction.
func (d *SiteReportWorkerDomain) CreateWorkerWithTx(ctx context.Context, tx *gorm.DB, param CreateWorkerParam) (int64, error) {
	id, err := d.resource.createWorkerWithTx(ctx, tx, param)
	if err != nil {
		return 0, log.LogError(err, nil)
	}
	return id, nil
}

// UpdateWorker updates an existing site report worker record.
func (d *SiteReportWorkerDomain) UpdateWorkerWithTx(ctx context.Context, tx *gorm.DB, param UpdateWorkerParam) error {
	err := d.resource.updateWorkerWithTx(ctx, tx, param)
	if err != nil {
		return log.LogError(err, nil)
	}
	return nil
}

// DeleteWorker deletes a site report worker record.
func (d *SiteReportWorkerDomain) DeleteWorkerWithTx(ctx context.Context, tx *gorm.DB, param DeleteWorkerParam) error {
	err := d.resource.deleteWorkerWithTx(ctx, tx, param)
	if err != nil {
		return log.LogError(err, nil)
	}
	return nil
}

// GetBySiteReportID retrieves site report worker records by site report ID.
func (d *SiteReportWorkerDomain) GetBySiteReportID(ctx context.Context, siteReportID int64) ([]SiteReportWorker, error) {
	workers, err := d.resource.getBySiteReportID(ctx, siteReportID)
	if err != nil {
		return []SiteReportWorker{}, log.LogError(err, nil)
	}
	return workers, nil
}

// GetWorkerReportList retrieves site report worker records with site report and user data filtered by date range.
func (d *SiteReportWorkerDomain) GetWorkerReportList(ctx context.Context, param GetWorkerReportListParam) ([]WorkerReportListItem, error) {
	workers, err := d.resource.getWorkerReportList(ctx, param)
	if err != nil {
		return []WorkerReportListItem{}, log.LogError(err, nil)
	}
	return workers, nil
}
