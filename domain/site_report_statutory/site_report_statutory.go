package sitereportstatutory

import (
	"context"

	"github.com/Sera-Global/be-nbs-accounting-system/common/log"
	"gorm.io/gorm"
)

type (
	SiteReportStatutoryDomainItf interface {
		CreateWithTx(ctx context.Context, tx *gorm.DB, param CreateParam) error
		UpdateWithTx(ctx context.Context, tx *gorm.DB, param UpdateParam) error
		ExistsBySiteReportIDWithTx(ctx context.Context, tx *gorm.DB, param ExistsBySiteReportIDParam) (bool, error)
		DeleteBySiteReportIDWithTx(ctx context.Context, tx *gorm.DB, param DeleteBySiteReportIDParam) error
		BulkUpdateStatutory(ctx context.Context, param BulkUpdateStatutoryParam) error
	}

	SiteReportStatutoryResourceItf interface {
		createWithTx(ctx context.Context, tx *gorm.DB, param CreateParam) error
		updateWithTx(ctx context.Context, tx *gorm.DB, param UpdateParam) error
		existsBySiteReportIDWithTx(ctx context.Context, tx *gorm.DB, param ExistsBySiteReportIDParam) (bool, error)
		deleteBySiteReportIDWithTx(ctx context.Context, tx *gorm.DB, param DeleteBySiteReportIDParam) error
		bulkUpdateStatutory(ctx context.Context, param BulkUpdateStatutoryParam) error
	}
)

// CreateWithTx creates a new site report statutory record within a transaction.
func (d *SiteReportStatutoryDomain) CreateWithTx(ctx context.Context, tx *gorm.DB, param CreateParam) error {
	err := d.resource.createWithTx(ctx, tx, param)
	if err != nil {
		return log.LogError(err, nil)
	}
	return nil
}

// UpdateWithTx updates an existing site report statutory record within a transaction.
func (d *SiteReportStatutoryDomain) UpdateWithTx(ctx context.Context, tx *gorm.DB, param UpdateParam) error {
	err := d.resource.updateWithTx(ctx, tx, param)
	if err != nil {
		return log.LogError(err, nil)
	}
	return nil
}

// ExistsBySiteReportIDWithTx checks if a site report statutory record exists for the given site report ID.
func (d *SiteReportStatutoryDomain) ExistsBySiteReportIDWithTx(ctx context.Context, tx *gorm.DB, param ExistsBySiteReportIDParam) (bool, error) {
	exists, err := d.resource.existsBySiteReportIDWithTx(ctx, tx, param)
	if err != nil {
		return false, log.LogError(err, nil)
	}
	return exists, nil
}

// DeleteBySiteReportIDWithTx deletes site report statutory records by site report ID within a transaction.
func (d *SiteReportStatutoryDomain) DeleteBySiteReportIDWithTx(ctx context.Context, tx *gorm.DB, param DeleteBySiteReportIDParam) error {
	err := d.resource.deleteBySiteReportIDWithTx(ctx, tx, param)
	if err != nil {
		return log.LogError(err, nil)
	}
	return nil
}

// BulkUpdateStatutory updates the is_statutory_issued status for multiple site report statutory records.
func (d *SiteReportStatutoryDomain) BulkUpdateStatutory(ctx context.Context, param BulkUpdateStatutoryParam) error {
	err := d.resource.bulkUpdateStatutory(ctx, param)
	if err != nil {
		return log.LogError(err, nil)
	}
	return nil
}
