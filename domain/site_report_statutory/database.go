package sitereportstatutory

import (
	"context"

	"github.com/Sera-Global/be-nbs-accounting-system/common/log"
	"github.com/Sera-Global/be-nbs-accounting-system/pkg/lib/dbmanager"
	"gorm.io/gorm"
)

// createWithTx creates a new site report statutory record within a transaction.
func (rsc SiteReportStatutoryResource) createWithTx(ctx context.Context, tx *gorm.DB, param CreateParam) error {
	db := tx.WithContext(ctx)

	statutory := SiteReportStatutory{
		SiteReportID:      param.SiteReportID,
		ExpensePerWorker:  param.ExpensePerWorker,
		Adjustment:        param.Adjustment,
		TotalAmount:       param.TotalAmount,
		Snapshot:          param.Snapshot,
		IsStatutoryIssued: param.IsStatutoryIssued,
		Note:              param.Note,
	}

	err := db.Create(&statutory).Error
	if err != nil {
		return log.LogError(err, nil)
	}

	return nil
}

// updateWithTx updates an existing site report statutory record within a transaction.
func (rsc SiteReportStatutoryResource) updateWithTx(ctx context.Context, tx *gorm.DB, param UpdateParam) error {
	db := tx.WithContext(ctx)

	updateFields := map[string]interface{}{
		"expense_per_worker":  param.ExpensePerWorker,
		"adjustment":          param.Adjustment,
		"total_amount":        param.TotalAmount,
		"snapshot":            param.Snapshot,
		"is_statutory_issued": param.IsStatutoryIssued,
		"note":                param.Note,
	}

	err := db.Model(&SiteReportStatutory{}).
		Where("site_report_id = ?", param.SiteReportID).
		Updates(updateFields).Error

	if err != nil {
		return log.LogError(err, nil)
	}

	return nil
}

// existsBySiteReportIDWithTx checks if a site report statutory record exists for the given site report ID.
func (rsc SiteReportStatutoryResource) existsBySiteReportIDWithTx(ctx context.Context, tx *gorm.DB, param ExistsBySiteReportIDParam) (bool, error) {
	db := tx.WithContext(ctx)

	var count int64
	err := db.Model(&SiteReportStatutory{}).
		Where("site_report_id = ?", param.SiteReportID).
		Count(&count).Error

	if err != nil {
		return false, log.LogError(err, nil)
	}

	return count > 0, nil
}

// deleteBySiteReportIDWithTx deletes site report statutory records by site report ID within a transaction.
func (rsc SiteReportStatutoryResource) deleteBySiteReportIDWithTx(ctx context.Context, tx *gorm.DB, param DeleteBySiteReportIDParam) error {
	db := tx.WithContext(ctx)

	err := db.Where("site_report_id = ?", param.SiteReportID).
		Delete(&SiteReportStatutory{}).Error

	if err != nil {
		return log.LogError(err, nil)
	}

	return nil
}

// bulkUpdateStatutory updates the is_statutory_issued status for multiple site report statutory records.
func (rsc SiteReportStatutoryResource) bulkUpdateStatutory(ctx context.Context, param BulkUpdateStatutoryParam) error {
	db := dbmanager.Manager().WithContext(ctx)

	// Build update map with the is_statutory_issued field
	updateFields := map[string]interface{}{
		"is_statutory_issued": param.IsStatutoryIssued,
	}

	// Perform bulk update for the specified site report statutory IDs
	err := db.Model(&SiteReportStatutory{}).
		Where("id IN ?", param.SiteReportStatutoryIDs).
		Updates(updateFields).Error

	if err != nil {
		return log.LogError(err, nil)
	}

	return nil
}
