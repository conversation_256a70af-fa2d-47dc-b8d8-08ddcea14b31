package sitereportstatutory

type SiteReportStatutory struct {
	ID                int64   `gorm:"column:id;primary_key"`
	SiteReportID      int64   `gorm:"column:site_report_id"`
	ExpensePerWorker  float64 `gorm:"column:expense_per_worker"`
	Adjustment        float64 `gorm:"column:adjustment"`
	TotalAmount       float64 `gorm:"column:total_amount"`
	Snapshot          string  `gorm:"column:snapshot;type:json"`
	IsStatutoryIssued bool    `gorm:"column:is_statutory_issued"`
	Note              string  `gorm:"column:note"`
}

type Snapshot struct {
	Rate float64 `json:"rate"`
}

// CreateParam represents the parameters for creating site report statutory
type CreateParam struct {
	SiteReportID      int64
	ExpensePerWorker  float64
	Adjustment        float64
	TotalAmount       float64
	Snapshot          string
	IsStatutoryIssued bool
	Note              string
}

// UpdateParam represents the parameters for updating site report statutory
type UpdateParam struct {
	SiteReportID      int64
	ExpensePerWorker  float64
	Adjustment        float64
	TotalAmount       float64
	Snapshot          string
	IsStatutoryIssued bool
	Note              string
}

// ExistsBySiteReportIDParam represents the parameters for checking if site report statutory exists by site report ID
type ExistsBySiteReportIDParam struct {
	SiteReportID int64
}

// DeleteBySiteReportIDParam represents the parameters for deleting site report statutory by site report ID
type DeleteBySiteReportIDParam struct {
	SiteReportID int64
}

// BulkUpdateStatutoryParam represents the parameters for bulk updating site report statutory status
type BulkUpdateStatutoryParam struct {
	SiteReportStatutoryIDs []int64
	IsStatutoryIssued      bool
}
