package sitereportoption

import (
	"context"

	"github.com/Sera-Global/be-nbs-accounting-system/common/log"
	"gorm.io/gorm"
)

// bulkCreateWithTx creates multiple site report option records within a transaction.
func (rsc SiteReportOptionResource) bulkCreateWithTx(ctx context.Context, tx *gorm.DB, param BulkCreateParam) error {
	db := tx.WithContext(ctx)

	// Pre-allocate slice with known capacity for better performance
	options := make([]SiteReportOption, 0, len(param.Options))
	for _, opt := range param.Options {
		options = append(options, SiteReportOption{
			SiteReportID: param.SiteReportID,
			OptionID:     opt.OptionID,
			Snapshot:     opt.Snapshot,
		})
	}

	if len(options) > 0 {
		err := db.Create(&options).Error
		if err != nil {
			return log.LogError(err, nil)
		}
	}

	return nil
}

// getBySiteReportIDWithTx retrieves site report option records by site report ID within a transaction.
func (rsc SiteReportOptionResource) getBySiteReportIDWithTx(ctx context.Context, tx *gorm.DB, param GetBySiteReportIDParam) ([]SiteReportOption, error) {
	db := tx.WithContext(ctx)

	var options []SiteReportOption
	err := db.Where("site_report_id = ?", param.SiteReportID).Find(&options).Error
	if err != nil {
		return []SiteReportOption{}, log.LogError(err, nil)
	}

	return options, nil
}

// bulkUpdateWithTx updates multiple site report option records within a transaction.
func (rsc SiteReportOptionResource) bulkUpdateWithTx(ctx context.Context, tx *gorm.DB, param BulkUpdateParam) error {
	db := tx.WithContext(ctx)

	// Use raw SQL for true bulk update with CASE statement for optimal performance
	if len(param.Options) == 1 {
		// For single update, use simple UPDATE for better readability
		option := param.Options[0]
		err := db.Model(&SiteReportOption{}).
			Where("site_report_id = ? AND option_id = ?", param.SiteReportID, option.OptionID).
			Update("snapshot", option.Snapshot).Error
		if err != nil {
			return log.LogError(err, nil)
		}
	} else {
		// For multiple updates, use bulk update with CASE statement
		optionIDs := make([]int64, 0, len(param.Options))
		caseStatements := ""
		args := []interface{}{}

		for i, option := range param.Options {
			optionIDs = append(optionIDs, option.OptionID)
			if i > 0 {
				caseStatements += " "
			}
			caseStatements += "WHEN option_id = ? THEN ?::json"
			args = append(args, option.OptionID, option.Snapshot)
		}

		// Append site_report_id arg after CASE parameters
		args = append(args, param.SiteReportID)

		// Add option IDs to args for the IN clause
		for _, optionID := range optionIDs {
			args = append(args, optionID)
		}

		// Build the bulk update query
		query := `UPDATE site_report_option
				  SET snapshot = CASE ` + caseStatements + ` END
				  WHERE site_report_id = ? AND option_id IN (?` +
			func() string {
				result := ""
				for i := 1; i < len(optionIDs); i++ {
					result += ",?"
				}
				return result
			}() + `)`

		err := db.Exec(query, args...).Error
		if err != nil {
			return log.LogError(err, nil)
		}
	}

	return nil
}

// bulkDeleteWithTx deletes multiple site report option records within a transaction.
func (rsc SiteReportOptionResource) bulkDeleteWithTx(ctx context.Context, tx *gorm.DB, param BulkDeleteParam) error {
	db := tx.WithContext(ctx)

	err := db.Where("site_report_id = ? AND option_id IN (?)", param.SiteReportID, param.OptionIDs).
		Delete(&SiteReportOption{}).Error

	if err != nil {
		return log.LogError(err, nil)
	}

	return nil
}
