package sitereportoption

import "github.com/Sera-Global/be-nbs-accounting-system/domain/option"

type SiteReportOption struct {
	ID           int64  `gorm:"column:id;primary_key"`
	SiteReportID int64  `gorm:"column:site_report_id"`
	OptionID     int64  `gorm:"column:option_id"`
	Snapshot     string `gorm:"column:snapshot;type:json"`

	// Foreign key relationship for option details
	Option option.Option `gorm:"foreignkey:OptionID"`
}

type Snapshot struct {
	Title  string  `json:"title"`
	Amount float64 `json:"amount"`
}

// OptionItem represents an option item for bulk creation
type OptionItem struct {
	OptionID int64
	Snapshot string
}

// BulkCreateParam represents the parameters for bulk creating site report options
type BulkCreateParam struct {
	SiteReportID int64
	Options      []OptionItem
}

// GetBySiteReportIDParam represents the parameters for getting site report options by site report ID
type GetBySiteReportIDParam struct {
	SiteReportID int64
}

// OptionUpdateData represents an option item for bulk update
type OptionUpdateData struct {
	OptionID int64
	Snapshot string
}

// BulkUpdateParam represents the parameters for bulk updating site report options
type BulkUpdateParam struct {
	SiteReportID int64
	Options      []OptionUpdateData
}

// BulkDeleteParam represents the parameters for bulk deleting site report options
type BulkDeleteParam struct {
	SiteReportID int64
	OptionIDs    []int64
}

// DeleteBySiteReportIDParam represents the parameters for deleting site report options by site report ID
type DeleteBySiteReportIDParam struct {
	SiteReportID int64
}
