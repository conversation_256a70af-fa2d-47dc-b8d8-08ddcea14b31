package sitereportoption

import (
	"context"

	"github.com/Sera-Global/be-nbs-accounting-system/common/log"
	"gorm.io/gorm"
)

type (
	SiteReportOptionDomainItf interface {
		GetBySiteReportIDWithTx(ctx context.Context, tx *gorm.DB, param GetBySiteReportIDParam) ([]SiteReportOption, error)
		BulkCreateWithTx(ctx context.Context, tx *gorm.DB, param BulkCreateParam) error
		BulkUpdateWithTx(ctx context.Context, tx *gorm.DB, param BulkUpdateParam) error
		BulkDeleteWithTx(ctx context.Context, tx *gorm.DB, param BulkDeleteParam) error
	}

	SiteReportOptionResourceItf interface {
		getBySiteReportIDWithTx(ctx context.Context, tx *gorm.DB, param GetBySiteReportIDParam) ([]SiteReportOption, error)
		bulkCreateWithTx(ctx context.Context, tx *gorm.DB, param BulkCreateParam) error
		bulkUpdateWithTx(ctx context.Context, tx *gorm.DB, param BulkUpdateParam) error
		bulkDeleteWithTx(ctx context.Context, tx *gorm.DB, param BulkDeleteParam) error
	}
)

// GetBySiteReportIDWithTx retrieves site report option records by site report ID within a transaction.
func (d *SiteReportOptionDomain) GetBySiteReportIDWithTx(ctx context.Context, tx *gorm.DB, param GetBySiteReportIDParam) ([]SiteReportOption, error) {
	options, err := d.resource.getBySiteReportIDWithTx(ctx, tx, param)
	if err != nil {
		return []SiteReportOption{}, log.LogError(err, nil)
	}
	return options, nil
}

// BulkCreateWithTx creates multiple site report option records within a transaction.
func (d *SiteReportOptionDomain) BulkCreateWithTx(ctx context.Context, tx *gorm.DB, param BulkCreateParam) error {
	err := d.resource.bulkCreateWithTx(ctx, tx, param)
	if err != nil {
		return log.LogError(err, nil)
	}
	return nil
}

// BulkUpdateWithTx updates multiple site report option records within a transaction.
func (d *SiteReportOptionDomain) BulkUpdateWithTx(ctx context.Context, tx *gorm.DB, param BulkUpdateParam) error {
	if len(param.Options) == 0 {
		return nil
	}
	err := d.resource.bulkUpdateWithTx(ctx, tx, param)
	if err != nil {
		return log.LogError(err, nil)
	}
	return nil
}

// BulkDeleteWithTx deletes multiple site report option records within a transaction.
func (d *SiteReportOptionDomain) BulkDeleteWithTx(ctx context.Context, tx *gorm.DB, param BulkDeleteParam) error {
	if len(param.OptionIDs) == 0 {
		return nil
	}
	err := d.resource.bulkDeleteWithTx(ctx, tx, param)
	if err != nil {
		return log.LogError(err, nil)
	}
	return nil
}
