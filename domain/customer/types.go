package customer

import (
	"time"

	"github.com/Sera-Global/be-nbs-accounting-system/common/types"
	"github.com/Sera-Global/be-nbs-accounting-system/domain/statutory"
)

type Customer struct {
	ID                int64      `gorm:"column:id;primary_key"`
	Code              string     `gorm:"column:code;unique"`
	Name              string     `gorm:"column:name"`
	Furigana          string     `gorm:"column:furigana"`
	PostCode          string     `gorm:"column:post_code"`
	AddressPrefecture string     `gorm:"column:address_prefecture"`
	AddressCity       string     `gorm:"column:address_city"`
	AddressBuilding   string     `gorm:"column:address_building"`
	Telephone         string     `gorm:"column:telephone"`
	Fax               string     `gorm:"column:fax"`
	BillingDate       time.Time  `gorm:"column:billing_date"`
	StatutoryID       int64      `gorm:"column:statutory_id"`
	CreatedAt         time.Time  `gorm:"column:created_at"`
	UpdatedAt         time.Time  `gorm:"column:updated_at"`
	DeletedAt         *time.Time `gorm:"column:deleted_at"`

	// Foreign key relationship for statutory
	Statutory statutory.Statutory `gorm:"foreignkey:StatutoryID"`
}

type GetListReq struct {
	types.BasicGetParam
}
