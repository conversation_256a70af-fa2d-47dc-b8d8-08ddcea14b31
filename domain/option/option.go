package option

import (
	"context"

	"github.com/Sera-Global/be-nbs-accounting-system/common/log"
)

type (
	OptionDomainItf interface {
		GetList(ctx context.Context, param GetListReq) ([]Option, error)
		GetByIDs(ctx context.Context, param GetByIDsParam) ([]Option, error)
	}

	OptionResourceItf interface {
		getList(ctx context.Context, param GetListReq) ([]Option, error)
		getByIDs(ctx context.Context, param GetByIDsParam) ([]Option, error)
	}
)

// GetList retrieves all options ordered by title.
func (d *OptionDomain) GetList(ctx context.Context, param GetListReq) ([]Option, error) {
	options, err := d.resource.getList(ctx, param)
	if err != nil {
		return []Option{}, log.LogError(err, nil)
	}
	return options, nil
}

// GetByIDs retrieves options by IDs.
func (d *OptionDomain) GetByIDs(ctx context.Context, param GetByIDsParam) ([]Option, error) {
	options, err := d.resource.getByIDs(ctx, param)
	if err != nil {
		return []Option{}, log.LogError(err, nil)
	}
	return options, nil
}
