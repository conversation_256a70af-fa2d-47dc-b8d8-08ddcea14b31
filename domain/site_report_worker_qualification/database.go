package sitereportworkerqualification

import (
	"context"

	"github.com/Sera-Global/be-nbs-accounting-system/common/log"
	"github.com/Sera-Global/be-nbs-accounting-system/pkg/lib/dbmanager"
	"gorm.io/gorm"
)

// bulkInsertWithTx creates multiple site report worker qualification records using optimized bulk insert.
func (rsc SiteReportWorkerQualificationResource) bulkInsertWithTx(ctx context.Context, tx *gorm.DB, param BulkInsertParam) error {
	db := tx.WithContext(ctx)

	// Pre-allocate slice with known capacity for better performance
	qualifications := make([]SiteReportWorkerQualification, 0, len(param.Qualifications))
	for _, qual := range param.Qualifications {
		qualifications = append(qualifications, SiteReportWorkerQualification{
			SiteReportWorkerID: param.SiteReportWorkerID,
			QualificationID:    qual.QualificationID,
			Snapshot:           qual.Snapshot,
		})
	}

	err := db.Create(&qualifications).Error
	if err != nil {
		return log.LogError(err, nil)
	}

	return nil
}

// bulkUpdateWithTx updates multiple site report worker qualification records using optimized bulk update.
func (rsc SiteReportWorkerQualificationResource) bulkUpdateWithTx(ctx context.Context, tx *gorm.DB, param BulkUpdateParam) error {
	db := tx.WithContext(ctx)

	// Use raw SQL for true bulk update with CASE statement for optimal performance
	// This performs a single UPDATE query instead of N individual queries
	if len(param.Qualifications) == 1 {
		// For single update, use simple UPDATE for better readability
		qual := param.Qualifications[0]
		err := db.Model(&SiteReportWorkerQualification{}).
			Where("site_report_worker_id = ? AND qualification_id = ?", param.SiteReportWorkerID, qual.QualificationID).
			Update("snapshot", qual.Snapshot).Error
		if err != nil {
			return log.LogError(err, nil)
		}
	} else {
		// For multiple updates, use bulk update with CASE statement
		qualificationIDs := make([]int64, 0, len(param.Qualifications))
		caseStatements := ""
		args := []interface{}{}

		for i, qual := range param.Qualifications {
			qualificationIDs = append(qualificationIDs, qual.QualificationID)
			if i > 0 {
				caseStatements += " "
			}
			caseStatements += "WHEN qualification_id = ? THEN ?::json"
			args = append(args, qual.QualificationID, qual.Snapshot)
		}

		// Append site_report_worker_id arg after CASE parameters
		args = append(args, param.SiteReportWorkerID)

		// Add qualification IDs for the IN clause
		for _, id := range qualificationIDs {
			args = append(args, id)
		}

		// Build the bulk update query
		query := `UPDATE site_report_worker_qualification
				  SET snapshot = CASE ` + caseStatements + ` END
				  WHERE site_report_worker_id = ? AND qualification_id IN (?` +
			func() string {
				result := ""
				for i := 1; i < len(qualificationIDs); i++ {
					result += ",?"
				}
				return result
			}() + `)`

		err := db.Exec(query, args...).Error
		if err != nil {
			return log.LogError(err, nil)
		}
	}

	return nil
}

// bulkDelete deletes multiple site report worker qualification records.
func (rsc SiteReportWorkerQualificationResource) bulkDeleteWithTx(ctx context.Context, tx *gorm.DB, param BulkDeleteParam) error {
	db := tx.WithContext(ctx)

	if param.SiteReportWorkerID > 0 {
		db = db.Where("site_report_worker_id = ?", param.SiteReportWorkerID)
	}

	if len(param.QualificationIDs) > 0 {
		db = db.Where("qualification_id IN ?", param.QualificationIDs)
	}

	err := db.Delete(&SiteReportWorkerQualification{}).Error

	if err != nil {
		return log.LogError(err, nil)
	}

	return nil
}

// getByWorkerID fetches site report worker qualifications by worker ID.
func (rsc SiteReportWorkerQualificationResource) getByWorkerID(ctx context.Context, param GetByWorkerIDParam) ([]SiteReportWorkerQualification, error) {
	var qualifications []SiteReportWorkerQualification

	db := dbmanager.Manager().WithContext(ctx)
	err := db.Where("site_report_worker_id = ?", param.SiteReportWorkerID).
		Find(&qualifications).Error

	if err != nil {
		return []SiteReportWorkerQualification{}, log.LogError(err, nil)
	}

	return qualifications, nil
}
