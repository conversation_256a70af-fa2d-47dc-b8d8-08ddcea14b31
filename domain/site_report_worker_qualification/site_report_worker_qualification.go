package sitereportworkerqualification

import (
	"context"

	"github.com/Sera-Global/be-nbs-accounting-system/common/log"
	"gorm.io/gorm"
)

type (
	SiteReportWorkerQualificationDomainItf interface {
		BulkInsertWithTx(ctx context.Context, tx *gorm.DB, param BulkInsertParam) error
		BulkUpdateWithTx(ctx context.Context, tx *gorm.DB, param BulkUpdateParam) error
		BulkDeleteWithTx(ctx context.Context, tx *gorm.DB, param BulkDeleteParam) error
		GetByWorkerID(ctx context.Context, param GetByWorkerIDParam) ([]SiteReportWorkerQualification, error)
	}

	SiteReportWorkerQualificationResourceItf interface {
		bulkInsertWithTx(ctx context.Context, tx *gorm.DB, param BulkInsertParam) error
		bulkUpdateWithTx(ctx context.Context, tx *gorm.DB, param BulkUpdateParam) error
		bulkDeleteWithTx(ctx context.Context, tx *gorm.DB, param BulkDeleteParam) error
		getByWorkerID(ctx context.Context, param GetByWorkerIDParam) ([]SiteReportWorkerQualification, error)
	}
)

// BulkInsertWithTx inserts multiple site report worker qualification records.
func (d *SiteReportWorkerQualificationDomain) BulkInsertWithTx(ctx context.Context, tx *gorm.DB, param BulkInsertParam) error {
	if len(param.Qualifications) == 0 {
		return nil
	}

	err := d.resource.bulkInsertWithTx(ctx, tx, param)
	if err != nil {
		return log.LogError(err, nil)
	}
	return nil
}

// BulkUpdateWithTx updates multiple site report worker qualification records.
func (d *SiteReportWorkerQualificationDomain) BulkUpdateWithTx(ctx context.Context, tx *gorm.DB, param BulkUpdateParam) error {
	if len(param.Qualifications) == 0 {
		return nil
	}

	err := d.resource.bulkUpdateWithTx(ctx, tx, param)
	if err != nil {
		return log.LogError(err, nil)
	}
	return nil
}

// BulkDeleteWithTx deletes multiple site report worker qualification records.
func (d *SiteReportWorkerQualificationDomain) BulkDeleteWithTx(ctx context.Context, tx *gorm.DB, param BulkDeleteParam) error {
	err := d.resource.bulkDeleteWithTx(ctx, tx, param)
	if err != nil {
		return log.LogError(err, nil)
	}
	return nil
}

// GetByWorkerID retrieves site report worker qualifications by worker ID.
func (d *SiteReportWorkerQualificationDomain) GetByWorkerID(ctx context.Context, param GetByWorkerIDParam) ([]SiteReportWorkerQualification, error) {
	qualifications, err := d.resource.getByWorkerID(ctx, param)
	if err != nil {
		return []SiteReportWorkerQualification{}, log.LogError(err, nil)
	}
	return qualifications, nil
}
