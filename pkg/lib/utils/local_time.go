package utils

import (
	"database/sql/driver"
	"fmt"
	"strings"
	"time"
)

// LocalTime is a custom time type that stores only time-of-day (HH:MM[:SS]).
// It implements sql.Scanner and driver.Valuer so it can be used transparently
// with GORM no matter whether the underlying DB column is TIME or VARCHAR.
// The zero value represents an unset/NULL time.
type LocalTime struct {
	time.Time
}

// layoutCandidates defines the layouts we attempt when parsing a time string.
var layoutCandidates = []string{
	"15:04:05", // full time
	"15:04",    // hours & minutes
}

// Scan implements the sql.Scanner interface.
// It accepts both string/[]byte and time.Time values coming from the DB.
func (lt *LocalTime) Scan(value interface{}) error {
	if value == nil {
		*lt = LocalTime{}
		return nil
	}

	switch v := value.(type) {
	case time.Time:
		lt.Time = v
		return nil
	case []byte:
		return lt.parse(string(v))
	case string:
		return lt.parse(v)
	default:
		return fmt.Errorf("cannot sql.Scan type %T into LocalTime", value)
	}
}

// Value implements the driver.Valuer interface so LocalTime can be persisted.
func (lt LocalTime) Value() (driver.Value, error) {
	if lt.IsZero() {
		return nil, nil
	}
	return lt.Format("15:04:05"), nil
}

// parse tries to parse the input string using the supported layouts.
func (lt *LocalTime) parse(s string) error {
	s = strings.TrimSpace(s)
	for _, layout := range layoutCandidates {
		if t, err := time.Parse(layout, s); err == nil {
			lt.Time = t
			return nil
		}
	}
	return fmt.Errorf("unable to parse LocalTime value: %s", s)
}

// MarshalJSON outputs the time in HH:MM format (without seconds) for JSON.
func (lt LocalTime) MarshalJSON() ([]byte, error) {
	if lt.IsZero() {
		return []byte("null"), nil
	}
	return []byte(fmt.Sprintf("\"%s\"", lt.Format("15:04"))), nil
}

// UnmarshalJSON parses a JSON string in HH:MM or HH:MM:SS format.
func (lt *LocalTime) UnmarshalJSON(b []byte) error {
	s := strings.Trim(string(b), "\"")
	if s == "null" || s == "" {
		*lt = LocalTime{}
		return nil
	}
	return lt.parse(s)
}
