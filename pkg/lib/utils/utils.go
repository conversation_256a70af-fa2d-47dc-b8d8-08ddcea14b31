package utils

import "slices"

// hasRole checks if the user has at least one of the specified roles.
// targetRoles is variadic, making the function flexible to accept one or more roles.
func HasRole(userRoles []string, targetRoles ...string) bool {
	for _, r := range targetRoles {
		if slices.Contains(userRoles, r) {
			return true
		}
	}
	return false
}

// SortStrings sorts a slice of strings in ascending order
func SortStrings(slice []string) {
	for i := 0; i < len(slice)-1; i++ {
		for j := i + 1; j < len(slice); j++ {
			if slice[i] > slice[j] {
				slice[i], slice[j] = slice[j], slice[i]
			}
		}
	}
}