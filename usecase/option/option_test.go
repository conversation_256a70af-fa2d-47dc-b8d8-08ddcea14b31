package option

import (
	"context"
	"errors"
	"testing"

	"github.com/Sera-Global/be-nbs-accounting-system/common/types"
	optionDmn "github.com/Sera-Global/be-nbs-accounting-system/domain/option"
)

// Mock domain for testing
type mockOptionDomain struct {
	options []optionDmn.Option
	err     error
}

func (m *mockOptionDomain) GetList(ctx context.Context, param optionDmn.GetListReq) ([]optionDmn.Option, error) {
	if m.err != nil {
		return []optionDmn.Option{}, m.err
	}

	// Filter by search if provided
	if param.Search != "" {
		var filtered []optionDmn.Option
		for _, opt := range m.options {
			// Simple case-insensitive contains check for testing
			if contains(opt.Title, param.Search) {
				filtered = append(filtered, opt)
			}
		}
		return filtered, nil
	}

	return m.options, nil
}

func (m *mockOptionDomain) GetByIDs(ctx context.Context, param optionDmn.GetByIDsParam) ([]optionDmn.Option, error) {
	if m.err != nil {
		return []optionDmn.Option{}, m.err
	}

	// Filter by IDs if provided
	if len(param.IDs) > 0 {
		var filtered []optionDmn.Option
		for _, opt := range m.options {
			for _, id := range param.IDs {
				if opt.ID == id {
					filtered = append(filtered, opt)
					break
				}
			}
		}
		return filtered, nil
	}

	return m.options, nil
}

// Helper function for case-insensitive contains check
func contains(s, substr string) bool {
	// Convert both strings to lowercase for case-insensitive comparison
	s = toLower(s)
	substr = toLower(substr)

	// Simple implementation for testing
	return len(s) >= len(substr) &&
		(s == substr ||
			(len(s) > len(substr) &&
				(s[:len(substr)] == substr ||
					s[len(s)-len(substr):] == substr ||
					findInString(s, substr))))
}

func findInString(s, substr string) bool {
	for i := 0; i <= len(s)-len(substr); i++ {
		if s[i:i+len(substr)] == substr {
			return true
		}
	}
	return false
}

// Simple toLower function for testing
func toLower(s string) string {
	result := make([]byte, len(s))
	for i := 0; i < len(s); i++ {
		if s[i] >= 'A' && s[i] <= 'Z' {
			result[i] = s[i] + 32
		} else {
			result[i] = s[i]
		}
	}
	return string(result)
}

func TestOptionUseCase_GetList_Success(t *testing.T) {
	// Test data
	mockData := []optionDmn.Option{
		{
			ID:       1,
			Title:    "Option 1",
			AddClaim: 7000,
		},
		{
			ID:       2,
			Title:    "Option 2",
			AddClaim: 8000,
		},
		{
			ID:       3,
			Title:    "Option 3",
			AddClaim: 9000,
		},
	}

	mockDomain := &mockOptionDomain{
		options: mockData,
		err:     nil,
	}

	uc := &OptionUseCase{
		option: mockDomain,
	}

	req := GetListReq{
		BasicGetParam: types.BasicGetParam{},
	}

	result, err := uc.GetList(context.Background(), req)

	// Assertions
	if err != nil {
		t.Errorf("Expected no error, got %v", err)
	}

	if len(result) != 3 {
		t.Errorf("Expected 3 results, got %d", len(result))
	}

	// Check first item
	if result[0].ID != 1 {
		t.Errorf("Expected ID 1, got %d", result[0].ID)
	}

	if result[0].Name != "Option 1" {
		t.Errorf("Expected name 'Option 1', got '%s'", result[0].Name)
	}

	if result[0].AddClaim != 7000 {
		t.Errorf("Expected AddClaim 7000, got %f", result[0].AddClaim)
	}

	// Check second item
	if result[1].ID != 2 {
		t.Errorf("Expected ID 2, got %d", result[1].ID)
	}

	if result[1].Name != "Option 2" {
		t.Errorf("Expected name 'Option 2', got '%s'", result[1].Name)
	}

	if result[1].AddClaim != 8000 {
		t.Errorf("Expected AddClaim 8000, got %f", result[1].AddClaim)
	}
}

func TestOptionUseCase_GetList_WithSearch_Success(t *testing.T) {
	// Test data
	mockData := []optionDmn.Option{
		{
			ID:       1,
			Title:    "Option AA",
			AddClaim: 7000,
		},
		{
			ID:       2,
			Title:    "Option AB",
			AddClaim: 8000,
		},
		{
			ID:       3,
			Title:    "Option BB",
			AddClaim: 9000,
		},
	}

	mockDomain := &mockOptionDomain{
		options: mockData,
		err:     nil,
	}

	uc := &OptionUseCase{
		option: mockDomain,
	}

	req := GetListReq{
		BasicGetParam: types.BasicGetParam{
			Search: "Option A",
		},
	}

	result, err := uc.GetList(context.Background(), req)

	// Assertions
	if err != nil {
		t.Errorf("Expected no error, got %v", err)
	}

	if len(result) != 2 {
		t.Errorf("Expected 2 results, got %d", len(result))
	}

	// Check that results contain only items with "Option A"
	for _, item := range result {
		if !contains(item.Name, "Option A") {
			t.Errorf("Expected result to contain 'Option A', got '%s'", item.Name)
		}
	}
}

func TestOptionUseCase_GetList_WithSearch_NoResults(t *testing.T) {
	// Test data
	mockData := []optionDmn.Option{
		{
			ID:       1,
			Title:    "Option AA",
			AddClaim: 7000,
		},
		{
			ID:       2,
			Title:    "Option AB",
			AddClaim: 8000,
		},
	}

	mockDomain := &mockOptionDomain{
		options: mockData,
		err:     nil,
	}

	uc := &OptionUseCase{
		option: mockDomain,
	}

	req := GetListReq{
		BasicGetParam: types.BasicGetParam{
			Search: "NonExistent",
		},
	}

	result, err := uc.GetList(context.Background(), req)

	// Assertions
	if err != nil {
		t.Errorf("Expected no error, got %v", err)
	}

	if len(result) != 0 {
		t.Errorf("Expected 0 results, got %d", len(result))
	}
}

func TestOptionUseCase_GetList_EmptyData(t *testing.T) {
	mockDomain := &mockOptionDomain{
		options: []optionDmn.Option{},
		err:     nil,
	}

	uc := &OptionUseCase{
		option: mockDomain,
	}

	req := GetListReq{
		BasicGetParam: types.BasicGetParam{},
	}

	result, err := uc.GetList(context.Background(), req)

	// Assertions
	if err != nil {
		t.Errorf("Expected no error, got %v", err)
	}

	if len(result) != 0 {
		t.Errorf("Expected 0 results, got %d", len(result))
	}
}

func TestOptionUseCase_GetList_DomainError(t *testing.T) {
	mockDomain := &mockOptionDomain{
		options: []optionDmn.Option{},
		err:     errors.New("database error"),
	}

	uc := &OptionUseCase{
		option: mockDomain,
	}

	req := GetListReq{
		BasicGetParam: types.BasicGetParam{},
	}

	result, err := uc.GetList(context.Background(), req)

	// Assertions
	if err == nil {
		t.Errorf("Expected error, got nil")
	}

	if len(result) != 0 {
		t.Errorf("Expected 0 results on error, got %d", len(result))
	}
}

func TestOptionUseCase_GetList_ResponseMapping(t *testing.T) {
	// Test that Title field is correctly mapped to Name in response
	mockData := []optionDmn.Option{
		{
			ID:          1,
			Code:        "OPT001",
			Title:       "Option Pattern 1",
			Explanation: "Test explanation",
			AddClaim:    7500.50,
			PaidAmount:  6000.25,
		},
	}

	mockDomain := &mockOptionDomain{
		options: mockData,
		err:     nil,
	}

	uc := &OptionUseCase{
		option: mockDomain,
	}

	req := GetListReq{
		BasicGetParam: types.BasicGetParam{},
	}

	result, err := uc.GetList(context.Background(), req)

	// Assertions
	if err != nil {
		t.Errorf("Expected no error, got %v", err)
	}

	if len(result) != 1 {
		t.Errorf("Expected 1 result, got %d", len(result))
	}

	// Check that Title is mapped to Name
	if result[0].Name != "Option Pattern 1" {
		t.Errorf("Expected Name 'Option Pattern 1', got '%s'", result[0].Name)
	}

	// Check that AddClaim is correctly mapped
	if result[0].AddClaim != 7500.50 {
		t.Errorf("Expected AddClaim 7500.50, got %f", result[0].AddClaim)
	}

	// Check that ID is correctly mapped
	if result[0].ID != 1 {
		t.Errorf("Expected ID 1, got %d", result[0].ID)
	}
}

func TestOptionUseCase_GetList_WithSearchEmptyString(t *testing.T) {
	// Test with empty search string - should return all results
	mockData := []optionDmn.Option{
		{
			ID:       1,
			Title:    "Option AA",
			AddClaim: 7000,
		},
		{
			ID:       2,
			Title:    "Option BB",
			AddClaim: 8000,
		},
	}

	mockDomain := &mockOptionDomain{
		options: mockData,
		err:     nil,
	}

	uc := &OptionUseCase{
		option: mockDomain,
	}

	req := GetListReq{
		BasicGetParam: types.BasicGetParam{
			Search: "", // Empty search
		},
	}

	result, err := uc.GetList(context.Background(), req)

	// Assertions
	if err != nil {
		t.Errorf("Expected no error, got %v", err)
	}

	if len(result) != 2 {
		t.Errorf("Expected 2 results, got %d", len(result))
	}
}

func TestOptionUseCase_GetList_WithSearchCaseInsensitive(t *testing.T) {
	// Test case-insensitive search
	mockData := []optionDmn.Option{
		{
			ID:       1,
			Title:    "Option Premium",
			AddClaim: 7000,
		},
		{
			ID:       2,
			Title:    "Basic Option",
			AddClaim: 8000,
		},
	}

	mockDomain := &mockOptionDomain{
		options: mockData,
		err:     nil,
	}

	uc := &OptionUseCase{
		option: mockDomain,
	}

	req := GetListReq{
		BasicGetParam: types.BasicGetParam{
			Search: "option", // lowercase search
		},
	}

	result, err := uc.GetList(context.Background(), req)

	// Assertions
	if err != nil {
		t.Errorf("Expected no error, got %v", err)
	}

	if len(result) != 2 {
		t.Errorf("Expected 2 results, got %d", len(result))
	}
}

func TestOptionUseCase_GetList_WithSearchPartialMatch(t *testing.T) {
	// Test partial string matching
	mockData := []optionDmn.Option{
		{
			ID:       1,
			Title:    "Premium Option Package",
			AddClaim: 7000,
		},
		{
			ID:       2,
			Title:    "Basic Service",
			AddClaim: 8000,
		},
		{
			ID:       3,
			Title:    "Standard Option",
			AddClaim: 9000,
		},
	}

	mockDomain := &mockOptionDomain{
		options: mockData,
		err:     nil,
	}

	uc := &OptionUseCase{
		option: mockDomain,
	}

	req := GetListReq{
		BasicGetParam: types.BasicGetParam{
			Search: "Option",
		},
	}

	result, err := uc.GetList(context.Background(), req)

	// Assertions
	if err != nil {
		t.Errorf("Expected no error, got %v", err)
	}

	if len(result) != 2 {
		t.Errorf("Expected 2 results, got %d", len(result))
	}

	// Verify the correct items are returned
	expectedTitles := map[string]bool{
		"Premium Option Package": false,
		"Standard Option":        false,
	}

	for _, item := range result {
		if _, exists := expectedTitles[item.Name]; exists {
			expectedTitles[item.Name] = true
		} else {
			t.Errorf("Unexpected result: %s", item.Name)
		}
	}

	// Check that all expected items were found
	for title, found := range expectedTitles {
		if !found {
			t.Errorf("Expected to find '%s' in results", title)
		}
	}
}
