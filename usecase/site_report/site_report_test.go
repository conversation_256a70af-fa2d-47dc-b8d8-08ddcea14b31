package sitereport

import (
	"testing"
	"time"

	sitereportDmn "github.com/Sera-Global/be-nbs-accounting-system/domain/site_report"
	"github.com/Sera-Global/be-nbs-accounting-system/pkg/lib/utils"
)

func TestAggregateReports(t *testing.T) {
	// Create test data
	date1, _ := time.Parse("2006-01-02", "2025-06-01")
	date2, _ := time.Parse("2006-01-02", "2025-06-01")
	date3, _ := time.Parse("2006-01-02", "2025-06-02")

	bStartTime, _ := time.Parse("15:04", "10:00")
	bEndTime, _ := time.Parse("15:04", "21:00")

	reports := []sitereportDmn.SiteReport{
		{
			ID:          1,
			WorkDate:    date1,
			SiteName:    "中村区役所等複合庁舎整備事業",
			Worker:      3,
			TotalAmount: 1000000,
			BStartTime:  utils.LocalTime{Time: bStartTime},
			BEndTime:    utils.LocalTime{Time: bEndTime},
			Note:        "摘要内容1",
			IsLocked:    true,
		},
		{
			ID:          2,
			WorkDate:    date2,
			SiteName:    "別の現場",
			Worker:      2,
			TotalAmount: 500000,
			BStartTime:  utils.LocalTime{Time: bStartTime},
			BEndTime:    utils.LocalTime{Time: bEndTime},
			Note:        "摘要内容2",
			IsLocked:    false,
		},
		{
			ID:          3,
			WorkDate:    date3,
			SiteName:    "第三の現場",
			Worker:      4,
			TotalAmount: 800000,
			BStartTime:  utils.LocalTime{Time: bStartTime},
			BEndTime:    utils.LocalTime{Time: bEndTime},
			Note:        "摘要内容3",
			IsLocked:    true,
		},
	}

	// Test aggregation
	result := aggregateReports(reports)

	// Verify structure
	if len(result) != 1 {
		t.Errorf("Expected 1 year, got %d", len(result))
	}

	year := result[0]
	if year.Year != "2025年" {
		t.Errorf("Expected year '2025年', got '%s'", year.Year)
	}

	if len(year.Month) != 1 {
		t.Errorf("Expected 1 month, got %d", len(year.Month))
	}

	month := year.Month[0]
	if month.Value != "06月" {
		t.Errorf("Expected month '06月', got '%s'", month.Value)
	}

	// Check aggregated totals for month
	expectedMonthWorker := int64(9)         // 3 + 2 + 4
	expectedMonthAmount := float64(2300000) // 1000000 + 500000 + 800000

	if month.Worker != expectedMonthWorker {
		t.Errorf("Expected month worker %d, got %d", expectedMonthWorker, month.Worker)
	}

	if month.TotalAmount != expectedMonthAmount {
		t.Errorf("Expected month total amount %.2f, got %.2f", expectedMonthAmount, month.TotalAmount)
	}

	// Check dates
	if len(month.Date) != 2 {
		t.Errorf("Expected 2 dates, got %d", len(month.Date))
	}

	// Check first date (should have 2 reports)
	date1Data := month.Date[0]
	if date1Data.Value != "1日" {
		t.Errorf("Expected date '1日', got '%s'", date1Data.Value)
	}

	if len(date1Data.Report) != 2 {
		t.Errorf("Expected 2 reports for date 1, got %d", len(date1Data.Report))
	}

	expectedDate1Worker := int64(5)         // 3 + 2
	expectedDate1Amount := float64(1500000) // 1000000 + 500000

	if date1Data.Worker != expectedDate1Worker {
		t.Errorf("Expected date 1 worker %d, got %d", expectedDate1Worker, date1Data.Worker)
	}

	if date1Data.TotalAmount != expectedDate1Amount {
		t.Errorf("Expected date 1 total amount %.2f, got %.2f", expectedDate1Amount, date1Data.TotalAmount)
	}

	// Check second date (should have 1 report)
	date2Data := month.Date[1]
	if date2Data.Value != "2日" {
		t.Errorf("Expected date '2日', got '%s'", date2Data.Value)
	}

	if len(date2Data.Report) != 1 {
		t.Errorf("Expected 1 report for date 2, got %d", len(date2Data.Report))
	}

	// Check report data format
	report := date1Data.Report[0]
	if report.SiteReportID != 1 {
		t.Errorf("Expected site report ID 1, got %d", report.SiteReportID)
	}

	if report.BStartTime != "10:00" {
		t.Errorf("Expected start time '10:00', got '%s'", report.BStartTime)
	}

	if report.BEndTime != "21:00" {
		t.Errorf("Expected end time '21:00', got '%s'", report.BEndTime)
	}
}

func TestSortInts(t *testing.T) {
	slice := []int{3, 1, 4, 1, 5, 9, 2, 6}
	sortInts(slice)

	expected := []int{1, 1, 2, 3, 4, 5, 6, 9}

	for i, v := range slice {
		if v != expected[i] {
			t.Errorf("Expected %d at index %d, got %d", expected[i], i, v)
		}
	}
}
