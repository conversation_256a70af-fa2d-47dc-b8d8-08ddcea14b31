package addfee

import (
	"context"

	"github.com/Sera-Global/be-nbs-accounting-system/common/log"
	dailyreportaddition "github.com/Sera-Global/be-nbs-accounting-system/domain/daily_report_addition"
)

func (uc *AddFeeUseCase) GetList(ctx context.Context, req GetListReq) ([]GetListResp, error) {
	// Get daily report additions from domain
	additions, err := uc.dailyReportAddition.GetList(ctx, dailyreportaddition.GetListReq{
		BasicGetParam: req.BasicGetParam,
	})
	if err != nil {
		return []GetListResp{}, log.LogError(err, nil)
	}
	if len(additions) == 0 {
		return []GetListResp{}, nil
	}

	// Convert to response format
	var resp []GetListResp
	for _, addition := range additions {
		resp = append(resp, GetListResp{
			ID:   addition.ID,
			Name: addition.Title,
		})
	}

	return resp, nil
}
