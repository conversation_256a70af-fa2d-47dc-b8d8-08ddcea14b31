package addfee

import (
	dailyreportadditionDmn "github.com/Sera-Global/be-nbs-accounting-system/domain/daily_report_addition"
)

type AddFeeUseCase struct {
	dailyReportAddition dailyreportadditionDmn.DailyReportAdditionDomainItf
}

type Domains struct {
	DailyReportAdditionDomain dailyreportadditionDmn.DailyReportAdditionDomainItf
}

func InitAddFeeUseCase(d Domains) *AddFeeUseCase {
	uc := &AddFeeUseCase{
		dailyReportAddition: d.DailyReportAdditionDomain,
	}
	return uc
}
