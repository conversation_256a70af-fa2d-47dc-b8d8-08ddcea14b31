package report

import (
	"encoding/json"
	"fmt"
	"html/template"
	"strconv"
	"strings"
	"time"

	"github.com/GoAdminGroup/go-admin/context"
	"github.com/GoAdminGroup/go-admin/modules/db"
	form2 "github.com/GoAdminGroup/go-admin/plugins/admin/modules/form"
	"github.com/GoAdminGroup/go-admin/plugins/admin/modules/table"
	"github.com/GoAdminGroup/go-admin/template/types"
	"github.com/GoAdminGroup/go-admin/template/types/form"

	constanta "github.com/Sera-Global/be-nbs-accounting-system/common/const"
	"github.com/Sera-Global/be-nbs-accounting-system/pkg/lib/dbmanager"
)

// GetIncomeTaxTable configures GoAdmin CRUD for the income_tax table.
func GetIncomeTaxTable(ctx *context.Context) table.Table {
	tbl := table.NewDefaultTable(ctx, table.Config{
		Driver:     db.DriverPostgresql,
		CanAdd:     true,
		Editable:   true,
		Deletable:  true,
		Exportable: true,
		Connection: table.DefaultConnectionName,
		PrimaryKey: table.PrimaryKey{
			Type: db.Bigint,
			Name: table.DefaultPrimaryKeyName,
		},
	})

	info := tbl.GetInfo()
	gormDB := dbmanager.Manager()

	// List fields
	info.AddField("ID", "id", db.Bigint).FieldSortable()
	info.AddField("加算額コード", "code", db.Varchar)
	info.AddField("以上", "more_than", db.Float)
	info.AddField("未満", "less_than", db.Float)

	// Display tax amounts horizontally
	info.AddField("税額設定", "amount", db.JSON).FieldDisplay(func(model types.FieldModel) interface{} {
		if model.Value == "" {
			return "No tax data"
		}

		var taxAmounts []map[string]interface{}
		if err := json.Unmarshal([]byte(model.Value), &taxAmounts); err != nil {
			return "Error parsing tax data"
		}

		if len(taxAmounts) == 0 {
			return "No tax data"
		}

		// Create a horizontal table display
		tableContent := `<table style="border-collapse: collapse; width: 100%;">`
		tableContent += `<tr style="background-color: #f5f5f5;">`
		for _, item := range taxAmounts {
			if typeStr, ok := item["type"].(string); ok {
				// Convert type to Japanese
				switch typeStr {
				case constanta.TaxDependentSingle:
					typeStr = "単身"
				case constanta.TaxDependent0:
					typeStr = "扶養0人"
				case constanta.TaxDependent1:
					typeStr = "扶養1人"
				case constanta.TaxDependent2:
					typeStr = "扶養2人"
				case constanta.TaxDependent3:
					typeStr = "扶養3人"
				case constanta.TaxDependent4:
					typeStr = "扶養4人"
				case constanta.TaxDependent5:
					typeStr = "扶養5人"
				case constanta.TaxDependent6:
					typeStr = "扶養6人"
				case constanta.TaxDependent7:
					typeStr = "扶養7人"
				}

				tableContent += fmt.Sprintf(`<th style="border: 1px solid #ddd; padding: 8px; text-align: center; min-width: 80px;">%s</th>`, typeStr)
			}
		}
		tableContent += `</tr><tr>`
		for _, item := range taxAmounts {
			if price, ok := item["price"].(float64); ok {
				tableContent += fmt.Sprintf(`<td style="border: 1px solid #ddd; padding: 8px; text-align: center;">%.0f</td>`, price)
			}
		}
		tableContent += `</tr></table>`

		return template.HTML(tableContent)
	})

	info.AddField("作成日", "created_at", db.Timestamp)
	info.AddField("更新日", "updated_at", db.Timestamp)

	info.SetTable("income_tax").SetTitle("所得税")

	// Soft delete implementation
	info.SetDeleteFn(func(ids []string) error {
		if gormDB == nil {
			return nil
		}
		return gormDB.Table("income_tax").Where("id IN ?", ids).Updates(map[string]interface{}{"deleted_at": time.Now()}).Error
	})

	// Exclude soft-deleted records
	info.WhereRaw("\"income_tax\".deleted_at IS NULL")

	// Form configuration
	formList := tbl.GetForm()

	formList.AddField("ID", "id", db.Bigint, form.Default).FieldNotAllowEdit()
	formList.AddField("加算額コード", "code", db.Varchar, form.Text).FieldMust()
	formList.AddField("以上", "more_than", db.Float, form.Number).FieldMust()
	formList.AddField("未満", "less_than", db.Float, form.Number).FieldMust()

	// Hidden JSON column to store tax amount data
	formList.AddField("税額設定", "amount", db.JSON, form.Text).
		FieldHide().
		FieldDefault("[]")

	// Add tax amount input fields for each dependency type
	taxTypes := []struct {
		Type  string
		Label string
	}{
		{constanta.TaxDependentSingle, "単身"},
		{constanta.TaxDependent0, "扶養0人"},
		{constanta.TaxDependent1, "扶養1人"},
		{constanta.TaxDependent2, "扶養2人"},
		{constanta.TaxDependent3, "扶養3人"},
		{constanta.TaxDependent4, "扶養4人"},
		{constanta.TaxDependent5, "扶養5人"},
		{constanta.TaxDependent6, "扶養6人"},
		{constanta.TaxDependent7, "扶養7人"},
	}

	for _, taxType := range taxTypes {
		col := fmt.Sprintf("tax_%s", taxType.Type)
		t := taxType.Type // capture loop variable
		formList.AddField(taxType.Label, col, db.Decimal, form.Text).
			FieldDisplay(func(val types.FieldModel) interface{} {
				if raw, ok := val.Row["amount"]; ok && raw != nil {
					var taxAmounts []map[string]interface{}
					switch v := raw.(type) {
					case string:
						_ = json.Unmarshal([]byte(v), &taxAmounts)
					case []byte:
						_ = json.Unmarshal(v, &taxAmounts)
					}
					for _, item := range taxAmounts {
						if itemType, _ := item["type"].(string); itemType == t {
							switch p := item["price"].(type) {
							case float64:
								return fmt.Sprintf("%.0f", p)
							case string:
								return p
							default:
								return fmt.Sprintf("%v", p)
							}
						}
					}
				}
				return ""
			}).
			FieldOptionExt(map[string]interface{}{
				"style": "width: 100px",
				"class": "tax-input",
			})
	}

	formList.AddField("作成日", "created_at", db.Timestamp, form.Datetime).
		FieldNowWhenInsert().
		FieldHideWhenCreate().
		FieldHideWhenUpdate().
		FieldNotAllowEdit()

	formList.AddField("更新日", "updated_at", db.Timestamp, form.Datetime).
		FieldNow().
		FieldHideWhenCreate().
		FieldHideWhenUpdate().
		FieldNotAllowEdit()

	formList.SetTable("income_tax").SetTitle("所得税")

	// Build amount JSON from tax type inputs before saving.
	formList.SetPreProcessFn(func(values form2.Values) form2.Values {
		var taxAmounts []map[string]interface{}

		taxTypes := []string{
			constanta.TaxDependentSingle,
			constanta.TaxDependent0,
			constanta.TaxDependent1,
			constanta.TaxDependent2,
			constanta.TaxDependent3,
			constanta.TaxDependent4,
			constanta.TaxDependent5,
			constanta.TaxDependent6,
			constanta.TaxDependent7,
		}

		for _, taxType := range taxTypes {
			key := fmt.Sprintf("tax_%s", taxType)
			priceStr := values.Get(key)
			// Remove the custom field so it won't attempt to insert into DB.
			delete(values, key)

			if priceStr == "" {
				priceStr = "0"
			}
			// Handle both comma and dot as decimal separator
			priceStr = strings.Replace(priceStr, ",", ".", 1)
			price, _ := strconv.ParseFloat(priceStr, 64)
			taxAmounts = append(taxAmounts, map[string]interface{}{"type": taxType, "price": price})
		}

		// Convert tax amounts data to JSON string
		b, err := json.Marshal(taxAmounts)
		if err != nil {
			fmt.Printf("Error marshaling tax amount data: %v\n", err)
			return values
		}

		// Set the amount field
		values.Add("amount", string(b))
		return values
	})

	return tbl
}
