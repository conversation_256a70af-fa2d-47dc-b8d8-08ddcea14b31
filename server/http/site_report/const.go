package site_report

import "errors"

var (
	ErrStartDateRequired     = errors.New("開始日は必須です")
	ErrEndDateRequired       = errors.New("終了日は必須です")
	ErrInvalidDateFormat     = errors.New("日付形式が無効です。YYYY-MM-DD形式で入力してください")
	ErrSiteReportIDsRequired = errors.New("サイトレポートIDは必須です")
	ErrNoFieldsProvided      = errors.New("更新するフィールドが指定されていません")
	ErrStartDateAfterEndDate = errors.New("開始日は終了日以降の日付を入力してください")

	// Calculation variable errors
	ErrCustomerIDRequired    = errors.New("customer_id is required")
	ErrBasicPriceIDRequired  = errors.New("basic_price_id is required")
	ErrStartTimeRequired     = errors.New("start_time is required")
	ErrEndTimeRequired       = errors.New("end_time is required")
	ErrInvalidTimeFormat     = errors.New("invalid time format. Use HH:MM:SS format")
	ErrStartTimeAfterEndTime = errors.New("start_time must be before end_time")

	// Worker calculation errors
	ErrNegativeTransportExpense = errors.New("transport_expense cannot be negative")
	ErrNegativeLeaderAllowance  = errors.New("leader_allowance cannot be negative")
	ErrInvalidQualificationID   = errors.New("qualification_allowance_ids must contain valid IDs greater than 0")

	// Detail list errors
	ErrWorkDateOrIDRequired           = errors.New("either work_date or id parameter is required")
	ErrWorkDateAndIDMutuallyExclusive = errors.New("work_date and id parameters are mutually exclusive")
	ErrInvalidWorkDateFormat          = errors.New("invalid work_date format. Use YYYY-MM-DD format")

	// Save worker errors
	ErrIDRequired       = errors.New("ID is required")
	ErrWorkerIDRequired = errors.New("worker_id is required and must be positive")

	// Calculation errors
	ErrInvalidDepartmentPicID       = errors.New("department_pic_id is required and must be positive")
	ErrInvalidBasicPriceID          = errors.New("basic_price_id is required and must be positive")
	ErrInvalidTotalWorker           = errors.New("total_worker is required and must be positive")
	ErrInvalidDistrictBlockID       = errors.New("district_block_id is required and must be positive")
	ErrInvalidDistrictBlockUnit     = errors.New("district_block_unit is required and must be positive")
	ErrInvalidDailyReportAdditionID = errors.New("daily_report_addition_id must be positive when provided")
	ErrInvalidTransitPlaceBlockID   = errors.New("transit_place_block_id must be positive when provided")
	ErrInvalidTransitPlaceBlockUnit = errors.New("transit_place_block_unit must be positive when provided")
	ErrInvalidLateEarlyWorker       = errors.New("late_early_worker cannot be negative")
	ErrInvalidTimeRange             = errors.New("b_end_time must be after b_start_time")
	ErrInvalidExtraTimeChargeWorker = errors.New("extra_time_charge total_worker must be positive")
	ErrInvalidOptionID              = errors.New("option id must be positive")

	ErrSiteNameRequired = errors.New("site_name is required")
	ErrBillDateRequired = errors.New("bill_date is required")

	// Statutory bulk update errors
	ErrSiteReportStatutoryIDsRequired = errors.New("site_report_statutory_ids is required")
	ErrIsStatutoryIssuedRequired      = errors.New("is_statutory_issued is required")
)
